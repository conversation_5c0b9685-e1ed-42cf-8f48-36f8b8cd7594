"use server";

import { unstable_cache, revalidateTag, revalidatePath } from "next/cache";
import { Task, TaskSortOption, createTask, deleteTask, deleteCompletedTasksByUserId, getTaskById, getTasksByUserId, getTasksByListId, getTasksByTagId, getUpcomingTasksByDueDate, getUpcomingTasksWithListsByDueDate, reorderTasks, updateTask, setTaskTags, duplicateTaskWithTags, moveTaskToList as moveTaskToListDb } from "@/lib/db";
import { TaskWithList } from "@/lib/types";

export const fetchTasks = unstable_cache(
  async (userId: string, sortOption: TaskSortOption = "position"): Promise<Task[]> => {
    console.log("Server action: fetchTasks called with userId:", userId);
    const tasks = await getTasksByUserId(userId, sortOption);
    console.log("Server action: fetchTasks returning:", tasks);
    return tasks;
  },
  ["tasks-jwt"],
  {
    tags: ["tasks"],
    revalidate: 600, // 10 minutes
  }
);

export const fetchTasksByList = unstable_cache(
  async (listId: string, sortOption: TaskSortOption = "position", requestingUserId?: string): Promise<Task[]> => {
    console.log("Server action: fetchTasksByList called with listId:", listId, "requestingUserId:", requestingUserId);
    const tasks = await getTasksByListId(listId, sortOption, requestingUserId);
    console.log("Server action: fetchTasksByList returning:", tasks);
    return tasks;
  },
  ["tasks-by-list-jwt"],
  {
    tags: ["tasks", "lists"],
    revalidate: 600, // 10 minutes
  }
);

export async function fetchTask(taskId: string): Promise<Task | null> {
  return getTaskById(taskId);
}

export const fetchTasksByTag = unstable_cache(
  async (tagId: string, userId: string, sortOption: TaskSortOption = "position"): Promise<Task[]> => {
    console.log("Server action: fetchTasksByTag called with tagId:", tagId, "userId:", userId);
    const tasks = await getTasksByTagId(tagId, userId, sortOption);
    console.log("Server action: fetchTasksByTag returning:", tasks);
    return tasks;
  },
  ["tasks-by-tag-jwt"],
  {
    tags: ["tasks", "tags"],
    revalidate: 600, // 10 minutes
  }
);

export async function addTask(
  userId: string,
  listId: string,
  data: {
    title: string;
    description?: string;
    due_date?: Date;
    status?: string;
    tagIds?: string[];
  }
): Promise<Task | null> {
  console.log("Server action: addTask called with userId:", userId, "listId:", listId, "data:", data);

  const { tagIds, ...taskData } = data;
  const task = await createTask(userId, listId, taskData);

  if (task && tagIds && tagIds.length > 0) {
    // Associate tags with the task
    await setTaskTags(task.id, tagIds, userId);
  }

  // Invalidate cache when task is added
  revalidateTag("tasks");
  revalidateTag("tags"); // For tag-filtered views
  revalidateTag("lists"); // For task counts

  console.log("Server action: addTask created task:", task);
  return task;
}

export async function editTask(
  taskId: string,
  userId: string,
  data: Partial<Omit<Task, "id" | "user_id" | "created_at" | "updated_at">>
): Promise<Task | null> {
  const result = await updateTask(taskId, userId, data);

  // Selective invalidation based on what changed
  revalidateTag("tasks");
  revalidateTag("tags"); // For tag-filtered views

  // Only invalidate task counts if status changed (affects completed/active counts)
  if (data.status) {
    revalidateTag("lists");
  }

  return result;
}

export async function removeTask(taskId: string, userId: string): Promise<boolean> {
  const result = await deleteTask(taskId, userId);

  // Invalidate cache when task is deleted
  revalidateTag("tasks");
  revalidateTag("tags"); // For tag-filtered views
  revalidateTag("lists"); // For task counts

  return result;
}

export async function duplicateTask(taskId: string, userId: string): Promise<Task | null> {
  console.log("Server action: duplicateTask called with taskId:", taskId, "userId:", userId);

  const duplicatedTask = await duplicateTaskWithTags(taskId, userId);

  // Invalidate cache when task is duplicated
  revalidateTag("tasks");
  revalidateTag("tags"); // For tag-filtered views
  revalidateTag("lists"); // For task counts

  console.log("Server action: duplicateTask created task:", duplicatedTask);
  return duplicatedTask;
}

export async function updateTaskOrder(userId: string, taskIds: string[]): Promise<boolean> {
  const result = await reorderTasks(userId, taskIds);

  // Invalidate cache when task order is updated
  revalidateTag("tasks");

  return result;
}

export async function moveTaskToList(
  taskId: string,
  userId: string,
  newListId: string
): Promise<Task | null> {
  console.log("Server action: moveTaskToList called with taskId:", taskId, "userId:", userId, "newListId:", newListId);

  const result = await moveTaskToListDb(taskId, userId, newListId);

  // Invalidate cache when task is moved between lists
  revalidateTag("tasks");
  revalidateTag("tags"); // For tag-filtered views
  revalidateTag("lists"); // For task counts

  console.log("Server action: moveTaskToList returning:", result);
  return result;
}

export const fetchUpcomingTasks = unstable_cache(
  async (userId: string): Promise<TaskWithList[]> => {
    const tasks = await getUpcomingTasksWithListsByDueDate(userId);
    return tasks as TaskWithList[];
  },
  ["upcoming-tasks-jwt"],
  {
    tags: ["tasks"],
    revalidate: 600, // 10 minutes
  }
);

export async function deleteCompletedTasks(userId: string): Promise<number> {
  console.log("Server action: deleteCompletedTasks called with userId:", userId);
  const deletedCount = await deleteCompletedTasksByUserId(userId);

  // Invalidate cache when completed tasks are deleted
  revalidateTag("tasks");
  revalidateTag("tags"); // For tag-filtered views
  revalidateTag("lists"); // For task counts

  console.log("Server action: deleteCompletedTasks deleted count:", deletedCount);
  return deletedCount;
}
