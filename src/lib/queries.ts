import { useState, useEffect, useMemo } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { List, Task, TaskSortOption, Tag, UserSettings } from '@/lib/db';
import {
  fetchLists,
  fetchList,
  fetchDefaultList,
  fetchTaskCounts,
  addList,
  editList,
  removeList,
  reorderUserLists
} from '@/app/actions/lists';
import {
  fetchTasksByList,
  fetchTasksByTag,
  addTask,
  editTask,
  removeTask,
  duplicateTask,
  updateTaskOrder,
  deleteCompletedTasks,
  moveTaskToList
} from '@/app/actions/tasks';
import {
  fetchTags,
  searchTags,
  createNewTag,
  editTag,
  removeTag,
  fetchTaskTags,
  addTaskTag,
  removeTaskTag,
  updateTaskTags
} from '@/app/actions/tags';
import {
  fetchUserSettings,
  updateSettings
} from '@/app/actions/settings';

// Query Keys
export const queryKeys = {
  lists: (userId: string) => ['lists', userId] as const,
  list: (listId: string) => ['list', listId] as const,
  defaultList: (userId: string) => ['defaultList', userId] as const,
  taskCounts: (userId: string) => ['taskCounts', userId] as const,
  tasks: (listId: string, sortOption: TaskSortOption = 'position') => ['tasks', listId, sortOption] as const,
  tasksByTag: (tagId: string, sortOption: TaskSortOption = 'position') => ['tasksByTag', tagId, sortOption] as const,
  tags: (userId: string) => ['tags', userId] as const,
  taskTags: (taskId: string) => ['taskTags', taskId] as const,
  bulkTaskTags: (listId: string) => ['bulkTaskTags', listId] as const,
  searchTags: (userId: string, searchTerm: string) => ['searchTags', userId, searchTerm] as const,
  userSettings: (userId: string) => ['userSettings', userId] as const,
};

// Lists Queries
export function useListsQuery(userId: string) {
  return useQuery({
    queryKey: queryKeys.lists(userId),
    queryFn: () => fetchLists(userId),
    enabled: !!userId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

export function useListQuery(listId: string) {
  return useQuery({
    queryKey: queryKeys.list(listId),
    queryFn: () => fetchList(listId),
    enabled: !!listId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

export function useDefaultListQuery(userId: string) {
  return useQuery({
    queryKey: queryKeys.defaultList(userId),
    queryFn: () => fetchDefaultList(userId),
    enabled: !!userId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

export function useTaskCountsQuery(userId: string) {
  return useQuery({
    queryKey: queryKeys.taskCounts(userId),
    queryFn: () => fetchTaskCounts(userId),
    enabled: !!userId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Tasks Queries
export function useTasksQuery(listId: string, sortOption: TaskSortOption = 'position', userId?: string) {
  return useQuery({
    queryKey: queryKeys.tasks(listId, sortOption),
    queryFn: () => fetchTasksByList(listId, sortOption, userId),
    enabled: !!listId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

export function useTasksByTagQuery(tagId: string, userId: string, sortOption: TaskSortOption = 'position') {
  return useQuery({
    queryKey: queryKeys.tasksByTag(tagId, sortOption),
    queryFn: () => fetchTasksByTag(tagId, userId, sortOption),
    enabled: !!tagId && !!userId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Tags Queries
export function useTagsQuery(userId: string) {
  return useQuery({
    queryKey: queryKeys.tags(userId),
    queryFn: () => fetchTags(userId),
    enabled: !!userId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

export function useTaskTagsQuery(taskId: string, userId: string, enabled: boolean = true) {
  return useQuery({
    queryKey: queryKeys.taskTags(taskId),
    queryFn: () => fetchTaskTags(taskId, userId),
    enabled: !!taskId && !!userId && enabled,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Bulk task tags query for better performance - TRUE bulk loading
export function useBulkTaskTagsQuery(listId: string, userId: string) {
  return useQuery({
    queryKey: queryKeys.bulkTaskTags(listId),
    queryFn: async () => {
      // Fetch all tasks for the list first
      const tasks = await fetchTasksByList(listId);

      if (tasks.length === 0) {
        return {};
      }

      // Extract task IDs
      const taskIds = tasks.map(task => task.id);

      // Import the bulk fetch function dynamically to avoid circular dependencies
      const { fetchBulkTaskTags } = await import('@/app/actions/tags');

      // Fetch tags for all tasks in ONE database query
      const taskTagsMap = await fetchBulkTaskTags(taskIds, userId);

      return taskTagsMap;
    },
    enabled: !!listId && !!userId,
    staleTime: 15 * 60 * 1000, // 15 minutes - tags don't change frequently
  });
}

export function useSearchTagsQuery(userId: string, searchTerm: string) {
  return useQuery({
    queryKey: queryKeys.searchTags(userId, searchTerm),
    queryFn: () => searchTags(userId, searchTerm),
    enabled: !!userId && !!searchTerm.trim(),
    staleTime: 5 * 60 * 1000, // 5 minutes for search results
  });
}

// Debounced search hook to prevent excessive API calls
export function useDebouncedSearchTags(userId: string, searchTerm: string, delay: number = 300) {
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState(searchTerm);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, delay);

    return () => {
      clearTimeout(timer);
    };
  }, [searchTerm, delay]);

  return useSearchTagsQuery(userId, debouncedSearchTerm);
}

// Hook to get filtered tags with client-side filtering for better performance
export function useFilteredTags(userId: string, searchTerm: string, selectedTags: Tag[] = []) {
  const { data: allTags = [] } = useTagsQuery(userId);
  const debouncedSearchQuery = useDebouncedSearchTags(userId, searchTerm.trim());

  return useMemo(() => {
    if (!searchTerm.trim()) {
      // For empty search, filter from all available tags
      return allTags.filter(tag =>
        !selectedTags.some(selected => selected.id === tag.id)
      );
    }

    // For search terms, use debounced search results
    const searchResults = debouncedSearchQuery.data || [];
    return searchResults.filter(tag =>
      !selectedTags.some(selected => selected.id === tag.id)
    );
  }, [allTags, debouncedSearchQuery.data, searchTerm, selectedTags]);
}

// List Mutations
export function useAddListMutation(userId: string) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ name, color, description }: { name: string; color?: string | null; description?: string | null }) => addList(userId, name, color, description),
    onSuccess: (newList) => {
      if (newList) {
        // Optimistically update lists cache
        queryClient.setQueryData(queryKeys.lists(userId), (old: List[] | undefined) => {
          return old ? [...old, newList] : [newList];
        });

        // Invalidate task counts to include new list
        queryClient.invalidateQueries({ queryKey: queryKeys.taskCounts(userId) });
      }
    },
  });
}

export function useEditListMutation(userId: string) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ listId, data }: { listId: string; data: { name?: string; description?: string | null; color?: string | null } }) =>
      editList(listId, userId, data),
    onMutate: async ({ listId, data }) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: queryKeys.lists(userId) });

      // Snapshot previous value
      const previousLists = queryClient.getQueryData(queryKeys.lists(userId));

      // Optimistically update lists cache
      if (previousLists) {
        const updatedLists = (previousLists as List[]).map(list =>
          list.id === listId ? { ...list, ...data } : list
        );
        queryClient.setQueryData(queryKeys.lists(userId), updatedLists);
      }

      // Optimistically update individual list cache
      const previousList = queryClient.getQueryData(queryKeys.list(listId));
      if (previousList) {
        const updatedList = { ...previousList as List, ...data };
        queryClient.setQueryData(queryKeys.list(listId), updatedList);
      }

      return { previousLists, previousList };
    },
    onError: (err, variables, context) => {
      // Rollback on error
      if (context?.previousLists) {
        queryClient.setQueryData(queryKeys.lists(userId), context.previousLists);
      }
      if (context?.previousList) {
        queryClient.setQueryData(queryKeys.list(variables.listId), context.previousList);
      }
    },
    onSuccess: (updatedList) => {
      if (updatedList) {
        // Update lists cache with server response
        queryClient.setQueryData(queryKeys.lists(userId), (old: List[] | undefined) => {
          return old?.map(list => list.id === updatedList.id ? updatedList : list);
        });

        // Update individual list cache with server response
        queryClient.setQueryData(queryKeys.list(updatedList.id), updatedList);
      }
    },
  });
}

export function useRemoveListMutation(userId: string) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (listId: string) => removeList(listId, userId),
    onSuccess: (success, listId) => {
      if (success) {
        // Remove from lists cache
        queryClient.setQueryData(queryKeys.lists(userId), (old: List[] | undefined) => {
          return old?.filter(list => list.id !== listId);
        });

        // Remove individual list cache
        queryClient.removeQueries({ queryKey: queryKeys.list(listId) });

        // Remove tasks cache for this list
        queryClient.removeQueries({ queryKey: ['tasks', listId] });

        // Invalidate task counts
        queryClient.invalidateQueries({ queryKey: queryKeys.taskCounts(userId) });
      }
    },
  });
}

export function useReorderListsMutation(userId: string) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (listIds: string[]) => reorderUserLists(userId, listIds),
    onMutate: async (listIds) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: queryKeys.lists(userId) });

      // Snapshot previous value
      const previousLists = queryClient.getQueryData(queryKeys.lists(userId));

      // Optimistically update
      if (previousLists) {
        const reorderedLists = listIds.map(id =>
          (previousLists as List[]).find(list => list.id === id)
        ).filter(Boolean) as List[];

        queryClient.setQueryData(queryKeys.lists(userId), reorderedLists);
      }

      return { previousLists };
    },
    onError: (err, listIds, context) => {
      // Rollback on error
      if (context?.previousLists) {
        queryClient.setQueryData(queryKeys.lists(userId), context.previousLists);
      }
    },
  });
}

// Task Mutations
export function useAddTaskMutation(listId: string, sortOption: TaskSortOption = 'position') {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ userId, title, description, due_date, tagIds }: {
      userId: string;
      title: string;
      description?: string;
      due_date?: Date;
      tagIds?: string[];
    }) => addTask(userId, listId, { title, description, due_date, tagIds }),
    onSuccess: (newTask, { userId }) => {
      if (newTask) {
        // Optimistically update tasks cache
        queryClient.setQueryData(queryKeys.tasks(listId, sortOption), (old: Task[] | undefined) => {
          return old ? [...old, newTask] : [newTask];
        });

        // Invalidate task counts
        queryClient.invalidateQueries({ queryKey: queryKeys.taskCounts(userId) });
      }
    },
  });
}

export function useEditTaskMutation(listId: string, sortOption: TaskSortOption = 'position') {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ taskId, userId, data }: {
      taskId: string;
      userId: string;
      data: Partial<Task>
    }) => editTask(taskId, userId, data),
    onMutate: async ({ taskId, data }) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: queryKeys.tasks(listId, sortOption) });

      // Snapshot previous value
      const previousTasks = queryClient.getQueryData(queryKeys.tasks(listId, sortOption));

      // Optimistically update
      if (previousTasks) {
        const updatedTasks = (previousTasks as Task[]).map(task =>
          task.id === taskId ? { ...task, ...data } : task
        );
        queryClient.setQueryData(queryKeys.tasks(listId, sortOption), updatedTasks);
      }

      return { previousTasks };
    },
    onError: (err, variables, context) => {
      // Rollback on error
      if (context?.previousTasks) {
        queryClient.setQueryData(queryKeys.tasks(listId, sortOption), context.previousTasks);
      }
    },
    onSuccess: (updatedTask, { userId, data }) => {
      if (updatedTask) {
        // Update tasks cache with server response
        queryClient.setQueryData(queryKeys.tasks(listId, sortOption), (old: Task[] | undefined) => {
          return old?.map(task => task.id === updatedTask.id ? updatedTask : task);
        });

        // CRITICAL: Invalidate all tag-based queries to ensure tag-filtered views show updates
        queryClient.invalidateQueries({
          queryKey: ['tasksByTag'],
          exact: false
        });

        // If task status changed, invalidate task counts and all task caches
        if (data.status) {
          queryClient.invalidateQueries({ queryKey: queryKeys.taskCounts(userId) });

          // Invalidate all task caches to ensure status changes are reflected everywhere
          queryClient.invalidateQueries({
            queryKey: ['tasks'],
            exact: false
          });
        }

        // If other properties changed, still invalidate task counts for consistency
        if (!data.status) {
          queryClient.invalidateQueries({ queryKey: queryKeys.taskCounts(userId) });
        }
      }
    },
  });
}

export function useRemoveTaskMutation(listId: string, sortOption: TaskSortOption = 'position') {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ taskId, userId }: { taskId: string; userId: string }) =>
      removeTask(taskId, userId),
    onSuccess: (success, { taskId, userId }) => {
      if (success) {
        // Remove from tasks cache
        queryClient.setQueryData(queryKeys.tasks(listId, sortOption), (old: Task[] | undefined) => {
          return old?.filter(task => task.id !== taskId);
        });

        // CRITICAL: Invalidate all tag-based queries to ensure tag-filtered views show updates
        queryClient.invalidateQueries({
          queryKey: ['tasksByTag'],
          exact: false
        });

        // Invalidate task counts
        queryClient.invalidateQueries({ queryKey: queryKeys.taskCounts(userId) });
      }
    },
  });
}

export function useDuplicateTaskMutation(listId: string, sortOption: TaskSortOption = 'position') {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ taskId, userId }: { taskId: string; userId: string }) =>
      duplicateTask(taskId, userId),
    onSuccess: (duplicatedTask, { taskId, userId }) => {
      if (duplicatedTask) {
        // Optimistically update tasks cache by inserting the duplicated task
        queryClient.setQueryData(queryKeys.tasks(listId, sortOption), (old: Task[] | undefined) => {
          if (!old) return [duplicatedTask];

          // Find the original task index and insert the duplicate right after it
          const originalIndex = old.findIndex(task => task.id === taskId);
          if (originalIndex !== -1) {
            const newTasks = [...old];
            newTasks.splice(originalIndex + 1, 0, duplicatedTask);
            return newTasks;
          }

          // If original task not found, append to end
          return [...old, duplicatedTask];
        });

        // CRITICAL: Invalidate all tag-based queries to ensure tag-filtered views show updates
        queryClient.invalidateQueries({
          queryKey: ['tasksByTag'],
          exact: false
        });

        // Invalidate task counts
        queryClient.invalidateQueries({ queryKey: queryKeys.taskCounts(userId) });
      }
    },
  });
}

export function useReorderTasksMutation(listId: string, sortOption: TaskSortOption = 'position') {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ userId, taskIds }: { userId: string; taskIds: string[] }) =>
      updateTaskOrder(userId, taskIds),
    onSuccess: () => {
      // Invalidate and refetch tasks to sync with database
      // This ensures the cache stays in sync without causing visual jumps
      queryClient.invalidateQueries({ queryKey: queryKeys.tasks(listId, sortOption) });
    },
  });
}

export function useMoveTaskToListMutation(currentListId: string, sortOption: TaskSortOption = 'position') {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ taskId, userId, newListId }: { taskId: string; userId: string; newListId: string }) =>
      moveTaskToList(taskId, userId, newListId),
    onMutate: async ({ taskId, newListId }) => {
      // Cancel outgoing refetches for both lists
      await queryClient.cancelQueries({ queryKey: queryKeys.tasks(currentListId, sortOption) });
      await queryClient.cancelQueries({ queryKey: queryKeys.tasks(newListId, sortOption) });

      // Snapshot previous values
      const previousCurrentListTasks = queryClient.getQueryData(queryKeys.tasks(currentListId, sortOption));
      const previousNewListTasks = queryClient.getQueryData(queryKeys.tasks(newListId, sortOption));

      // Optimistically update - remove from current list
      if (previousCurrentListTasks) {
        const updatedCurrentTasks = (previousCurrentListTasks as Task[]).filter(task => task.id !== taskId);
        queryClient.setQueryData(queryKeys.tasks(currentListId, sortOption), updatedCurrentTasks);
      }

      // Optimistically update - add to new list (at the top)
      if (previousNewListTasks) {
        const taskToMove = (previousCurrentListTasks as Task[])?.find(task => task.id === taskId);
        if (taskToMove) {
          const updatedTask = { ...taskToMove, list_id: newListId };
          const updatedNewTasks = [updatedTask, ...(previousNewListTasks as Task[])];
          queryClient.setQueryData(queryKeys.tasks(newListId, sortOption), updatedNewTasks);
        }
      }

      return { previousCurrentListTasks, previousNewListTasks };
    },
    onSuccess: (movedTask, { userId, newListId }) => {
      if (movedTask) {
        // Invalidate both lists to ensure consistency
        queryClient.invalidateQueries({ queryKey: queryKeys.tasks(currentListId, sortOption) });
        queryClient.invalidateQueries({ queryKey: queryKeys.tasks(newListId, sortOption) });

        // CRITICAL: Invalidate all tag-based queries to ensure tag-filtered views show updates
        queryClient.invalidateQueries({
          queryKey: ['tasksByTag'],
          exact: false
        });

        // Invalidate task counts
        queryClient.invalidateQueries({ queryKey: queryKeys.taskCounts(userId) });
      }
    },
    onError: (err, { newListId }, context) => {
      // Rollback on error
      if (context?.previousCurrentListTasks) {
        queryClient.setQueryData(queryKeys.tasks(currentListId, sortOption), context.previousCurrentListTasks);
      }
      if (context?.previousNewListTasks) {
        queryClient.setQueryData(queryKeys.tasks(newListId, sortOption), context.previousNewListTasks);
      }
    },
  });
}

export function useDeleteCompletedTasksMutation(listId: string, sortOption: TaskSortOption = 'position') {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (userId: string) => deleteCompletedTasks(userId),
    onSuccess: (deletedCount, userId) => {
      if (deletedCount > 0) {
        // Remove completed tasks from cache
        queryClient.setQueryData(queryKeys.tasks(listId, sortOption), (old: Task[] | undefined) => {
          return old?.filter(task => task.status !== 'completed');
        });

        // CRITICAL: Invalidate all tag-based queries to ensure tag-filtered views show updates
        queryClient.invalidateQueries({
          queryKey: ['tasksByTag'],
          exact: false
        });

        // Invalidate task counts
        queryClient.invalidateQueries({ queryKey: queryKeys.taskCounts(userId) });
      }
    },
  });
}

// Tag Mutations
export function useCreateTagMutation(userId: string) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ name, color }: { name: string; color: string }) =>
      createNewTag(userId, name, color),
    onSuccess: (newTag) => {
      if (newTag) {
        // Optimistically update tags cache
        queryClient.setQueryData(queryKeys.tags(userId), (old: Tag[] | undefined) => {
          return old ? [...old, newTag] : [newTag];
        });

        // Invalidate search results to include new tag
        queryClient.invalidateQueries({
          queryKey: ['searchTags', userId],
          exact: false
        });
      }
    },
  });
}

export function useEditTagMutation(userId: string) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ tagId, data }: { tagId: string; data: { name?: string; color?: string } }) =>
      editTag(tagId, userId, data),
    onMutate: async ({ tagId, data }) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: queryKeys.tags(userId) });

      // Snapshot previous value
      const previousTags = queryClient.getQueryData(queryKeys.tags(userId));

      // Optimistically update
      if (previousTags) {
        const updatedTags = (previousTags as Tag[]).map(tag =>
          tag.id === tagId ? { ...tag, ...data } : tag
        );
        queryClient.setQueryData(queryKeys.tags(userId), updatedTags);
      }

      return { previousTags };
    },
    onError: (err, variables, context) => {
      // Rollback on error
      if (context?.previousTags) {
        queryClient.setQueryData(queryKeys.tags(userId), context.previousTags);
      }
    },
    onSuccess: (updatedTag) => {
      if (updatedTag) {
        // Update tags cache with server response
        queryClient.setQueryData(queryKeys.tags(userId), (old: Tag[] | undefined) => {
          return old?.map(tag => tag.id === updatedTag.id ? updatedTag : tag);
        });

        // Update all task tags caches that contain this tag
        queryClient.invalidateQueries({
          queryKey: ['taskTags'],
          exact: false
        });

        // Invalidate all bulk task tags caches since tag data changed
        queryClient.invalidateQueries({
          queryKey: ['bulkTaskTags'],
          exact: false
        });

        // Update all tasks caches to reflect tag name changes in task display
        queryClient.invalidateQueries({
          queryKey: ['tasks'],
          exact: false
        });

        // Invalidate search results
        queryClient.invalidateQueries({
          queryKey: ['searchTags', userId],
          exact: false
        });
      }
    },
  });
}

export function useDeleteTagMutation(userId: string) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (tagId: string) => removeTag(tagId, userId),
    onSuccess: (success, tagId) => {
      if (success) {
        // Remove from tags cache
        queryClient.setQueryData(queryKeys.tags(userId), (old: Tag[] | undefined) => {
          return old?.filter(tag => tag.id !== tagId);
        });

        // Invalidate search results
        queryClient.invalidateQueries({
          queryKey: ['searchTags', userId],
          exact: false
        });

        // Invalidate all task tags that might contain this tag
        queryClient.invalidateQueries({
          queryKey: ['taskTags'],
          exact: false
        });

        // Invalidate all bulk task tags caches since tag was deleted
        queryClient.invalidateQueries({
          queryKey: ['bulkTaskTags'],
          exact: false
        });
      }
    },
  });
}

export function useAddTaskTagMutation(taskId: string, userId: string, listId?: string) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (tagId: string) => addTaskTag(taskId, tagId, userId),
    onMutate: async (tagId) => {
      // Cancel any outgoing refetches for both individual and bulk caches
      await queryClient.cancelQueries({ queryKey: queryKeys.taskTags(taskId) });
      if (listId) {
        await queryClient.cancelQueries({ queryKey: queryKeys.bulkTaskTags(listId) });
      }

      // Snapshot the previous values
      const previousTaskTags = queryClient.getQueryData(queryKeys.taskTags(taskId));
      const previousBulkTags = listId ? queryClient.getQueryData(queryKeys.bulkTaskTags(listId)) : undefined;

      // Get the tag data for optimistic update
      const allTags = queryClient.getQueryData(queryKeys.tags(userId)) as Tag[] || [];
      const tag = allTags.find(t => t.id === tagId);

      if (tag) {
        // Optimistically update individual task tags
        queryClient.setQueryData(queryKeys.taskTags(taskId), (old: Tag[] | undefined) => {
          const current = old || [];
          return current.some(t => t.id === tagId) ? current : [...current, tag];
        });

        // Optimistically update bulk task tags cache if listId is provided
        if (listId) {
          queryClient.setQueryData(queryKeys.bulkTaskTags(listId), (old: Record<string, Tag[]> | undefined) => {
            if (!old) return old;
            const current = old[taskId] || [];
            return {
              ...old,
              [taskId]: current.some(t => t.id === tagId) ? current : [...current, tag]
            };
          });
        }
      }

      return { previousTaskTags, previousBulkTags };
    },
    onSuccess: (success) => {
      if (success) {
        // Invalidate individual task tags cache
        queryClient.invalidateQueries({ queryKey: queryKeys.taskTags(taskId) });

        // Invalidate bulk task tags cache for the specific list
        if (listId) {
          queryClient.invalidateQueries({ queryKey: queryKeys.bulkTaskTags(listId) });
        }

        // CRITICAL: Invalidate all tag-based queries to ensure tag-filtered views show updates
        queryClient.invalidateQueries({
          queryKey: ['tasksByTag'],
          exact: false
        });
      }
    },
    onError: (_err, _tagId, context) => {
      // Rollback optimistic updates
      if (context?.previousTaskTags) {
        queryClient.setQueryData(queryKeys.taskTags(taskId), context.previousTaskTags);
      }
      if (context?.previousBulkTags && listId) {
        queryClient.setQueryData(queryKeys.bulkTaskTags(listId), context.previousBulkTags);
      }
    },
  });
}



export function useRemoveTaskTagMutation(taskId: string, userId: string, listId?: string) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (tagId: string) => removeTaskTag(taskId, tagId, userId),
    onMutate: async (tagId) => {
      // Cancel any outgoing refetches for both individual and bulk caches
      await queryClient.cancelQueries({ queryKey: queryKeys.taskTags(taskId) });
      if (listId) {
        await queryClient.cancelQueries({ queryKey: queryKeys.bulkTaskTags(listId) });
      }

      // Snapshot the previous values
      const previousTaskTags = queryClient.getQueryData(queryKeys.taskTags(taskId));
      const previousBulkTags = listId ? queryClient.getQueryData(queryKeys.bulkTaskTags(listId)) : undefined;

      // Optimistically update individual task tags
      queryClient.setQueryData(queryKeys.taskTags(taskId), (old: Tag[] | undefined) => {
        return (old || []).filter(tag => tag.id !== tagId);
      });

      // Optimistically update bulk task tags cache if listId is provided
      if (listId) {
        queryClient.setQueryData(queryKeys.bulkTaskTags(listId), (old: Record<string, Tag[]> | undefined) => {
          if (!old) return old;
          return {
            ...old,
            [taskId]: (old[taskId] || []).filter(tag => tag.id !== tagId)
          };
        });
      }

      return { previousTaskTags, previousBulkTags };
    },
    onSuccess: (success) => {
      if (success) {
        // Invalidate individual task tags cache
        queryClient.invalidateQueries({ queryKey: queryKeys.taskTags(taskId) });

        // Invalidate bulk task tags cache for the specific list
        if (listId) {
          queryClient.invalidateQueries({ queryKey: queryKeys.bulkTaskTags(listId) });
        }

        // CRITICAL: Invalidate all tag-based queries to ensure tag-filtered views show updates
        queryClient.invalidateQueries({
          queryKey: ['tasksByTag'],
          exact: false
        });
      }
    },
    onError: (_err, _tagId, context) => {
      // Rollback optimistic updates
      if (context?.previousTaskTags) {
        queryClient.setQueryData(queryKeys.taskTags(taskId), context.previousTaskTags);
      }
      if (context?.previousBulkTags && listId) {
        queryClient.setQueryData(queryKeys.bulkTaskTags(listId), context.previousBulkTags);
      }
    },
  });
}

export function useUpdateTaskTagsMutation(taskId: string, userId: string, listId?: string) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (tagIds: string[]) => updateTaskTags(taskId, tagIds, userId),
    onSuccess: (success) => {
      if (success) {
        // Invalidate individual task tags cache
        queryClient.invalidateQueries({ queryKey: queryKeys.taskTags(taskId) });

        // Invalidate bulk task tags cache for the specific list
        if (listId) {
          queryClient.invalidateQueries({ queryKey: queryKeys.bulkTaskTags(listId) });
        }

        // Invalidate all tasks queries to update task display
        queryClient.invalidateQueries({
          queryKey: ['tasks'],
          exact: false
        });

        // CRITICAL: Invalidate all tag-based queries to ensure tag-filtered views show updates
        queryClient.invalidateQueries({
          queryKey: ['tasksByTag'],
          exact: false
        });
      }
    },
  });
}

// Enhanced prefetching utilities with tag data
export function usePrefetchAdjacentLists(
  lists: List[],
  currentListId: string | null,
  sortOption: TaskSortOption = 'position',
  userId?: string
) {
  const queryClient = useQueryClient();

  const prefetchAdjacentLists = () => {
    if (!currentListId || !lists.length) return;

    const currentIndex = lists.findIndex(list => list.id === currentListId);
    if (currentIndex === -1) return;

    // Prefetch previous list with tags
    if (currentIndex > 0) {
      const prevList = lists[currentIndex - 1];

      // Prefetch tasks
      queryClient.prefetchQuery({
        queryKey: queryKeys.tasks(prevList.id, sortOption),
        queryFn: () => fetchTasksByList(prevList.id, sortOption),
        staleTime: 15 * 60 * 1000, // 15 minutes
      });

      // Prefetch bulk task tags if userId is available - using TRUE bulk loading
      if (userId) {
        queryClient.prefetchQuery({
          queryKey: queryKeys.bulkTaskTags(prevList.id),
          queryFn: async () => {
            const tasks = await fetchTasksByList(prevList.id);
            if (tasks.length === 0) return {};

            const taskIds = tasks.map(task => task.id);
            const { fetchBulkTaskTags } = await import('@/app/actions/tags');
            return await fetchBulkTaskTags(taskIds, userId);
          },
          staleTime: 15 * 60 * 1000, // 15 minutes
        });
      }
    }

    // Prefetch next list with tags
    if (currentIndex < lists.length - 1) {
      const nextList = lists[currentIndex + 1];

      // Prefetch tasks with user validation
      queryClient.prefetchQuery({
        queryKey: queryKeys.tasks(nextList.id, sortOption),
        queryFn: () => fetchTasksByList(nextList.id, sortOption, userId),
        staleTime: 15 * 60 * 1000, // 15 minutes
      });

      // Prefetch bulk task tags if userId is available - using TRUE bulk loading
      if (userId) {
        queryClient.prefetchQuery({
          queryKey: queryKeys.bulkTaskTags(nextList.id),
          queryFn: async () => {
            const tasks = await fetchTasksByList(nextList.id, 'position', userId);
            if (tasks.length === 0) return {};

            const taskIds = tasks.map(task => task.id);
            const { fetchBulkTaskTags } = await import('@/app/actions/tags');
            return await fetchBulkTaskTags(taskIds, userId);
          },
          staleTime: 15 * 60 * 1000, // 15 minutes
        });
      }
    }
  };

  return { prefetchAdjacentLists };
}

// Enhanced utility to prefetch all lists with tags for instant navigation
export function usePrefetchAllLists(lists: List[], sortOption: TaskSortOption = 'position', userId?: string) {
  const queryClient = useQueryClient();

  const prefetchAllLists = () => {
    lists.forEach(list => {
      // Prefetch tasks with user validation
      queryClient.prefetchQuery({
        queryKey: queryKeys.tasks(list.id, sortOption),
        queryFn: () => fetchTasksByList(list.id, sortOption, userId),
        staleTime: 15 * 60 * 1000, // 15 minutes
      });

      // Prefetch bulk task tags if userId is available - using TRUE bulk loading
      if (userId) {
        queryClient.prefetchQuery({
          queryKey: queryKeys.bulkTaskTags(list.id),
          queryFn: async () => {
            const tasks = await fetchTasksByList(list.id, sortOption, userId);
            if (tasks.length === 0) return {};

            const taskIds = tasks.map(task => task.id);
            const { fetchBulkTaskTags } = await import('@/app/actions/tags');
            return await fetchBulkTaskTags(taskIds, userId);
          },
          staleTime: 15 * 60 * 1000, // 15 minutes
        });
      }
    });
  };

  return { prefetchAllLists };
}

// Optimized hook to get task tags with bulk loading to prevent pop-in
export function useOptimizedTaskTags(taskId: string, listId: string, userId: string, forceIndividual: boolean = false) {
  // If forceIndividual is true (e.g., in tag-filtered view), skip bulk loading
  const shouldUseBulk = !forceIndividual && !!listId;

  // Try bulk loading only if not forced to use individual queries
  // We'll conditionally call the hook but disable it when not needed
  const { data: bulkTaskTags, isLoading: bulkLoading, error: bulkError } = useQuery({
    queryKey: queryKeys.bulkTaskTags(listId),
    queryFn: async () => {
      // Fetch all tasks for the list first
      const tasks = await fetchTasksByList(listId);

      if (tasks.length === 0) {
        return {};
      }

      // Extract task IDs
      const taskIds = tasks.map(task => task.id);

      // Import the bulk fetch function dynamically to avoid circular dependencies
      const { fetchBulkTaskTags } = await import('@/app/actions/tags');

      // Fetch tags for all tasks in ONE database query
      const taskTagsMap = await fetchBulkTaskTags(taskIds, userId);

      return taskTagsMap;
    },
    enabled: shouldUseBulk && !!listId && !!userId,
    staleTime: 15 * 60 * 1000, // 15 minutes - tags don't change frequently
  });

  // Simplified logic: if we have bulk data, use it; otherwise use individual query
  const hasBulkData = shouldUseBulk && bulkTaskTags && typeof bulkTaskTags === 'object';
  const taskTagsFromBulk = hasBulkData ? (bulkTaskTags[taskId] || []) : [];

  // Use individual query if bulk loading failed, is disabled, or we're forcing individual queries
  const shouldUseFallback = !shouldUseBulk || (!bulkLoading && !hasBulkData && !!bulkError);

  const { data: individualTaskTags = [], isLoading: individualLoading } = useTaskTagsQuery(
    taskId,
    userId,
    shouldUseFallback
  );

  // Determine final data and loading state
  const finalTags = hasBulkData ? taskTagsFromBulk : (shouldUseFallback ? individualTaskTags : []);
  const finalLoading = hasBulkData ? false : (shouldUseFallback ? individualLoading : bulkLoading);



  return {
    data: finalTags,
    isLoading: finalLoading,
    // Data is considered fresh if we have bulk data or individual data
    isFresh: hasBulkData || shouldUseFallback
  };
}

// Settings Queries
export function useUserSettingsQuery(userId: string) {
  return useQuery({
    queryKey: queryKeys.userSettings(userId),
    queryFn: () => fetchUserSettings(userId),
    enabled: !!userId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Settings Mutations
export function useUpdateSettingsMutation(userId: string) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: Partial<UserSettings>) => updateSettings(userId, data),
    onMutate: async (data) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: queryKeys.userSettings(userId) });

      // Snapshot previous value
      const previousSettings = queryClient.getQueryData(queryKeys.userSettings(userId));

      // Optimistically update
      if (previousSettings) {
        const updatedSettings = { ...previousSettings as UserSettings, ...data };
        queryClient.setQueryData(queryKeys.userSettings(userId), updatedSettings);
      }

      return { previousSettings };
    },
    onError: (err, variables, context) => {
      // Rollback on error
      if (context?.previousSettings) {
        queryClient.setQueryData(queryKeys.userSettings(userId), context.previousSettings);
      }
    },
    onSuccess: (updatedSettings) => {
      if (updatedSettings) {
        // Update cache with server response
        queryClient.setQueryData(queryKeys.userSettings(userId), updatedSettings);
      }
    },
  });
}
